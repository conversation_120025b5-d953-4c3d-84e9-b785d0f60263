using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;

namespace CommunityToolkit.Mvvm.ComponentModel.__Internals;

[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
[DebuggerNonUserCode]
[ExcludeFromCodeCoverage]
[EditorBrowsable(EditorBrowsableState.Never)]
[Obsolete("This type is not intended to be used directly by user code")]
internal static class __KnownINotifyPropertyChangedArgs
{
	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs 显示功能按钮 = new PropertyChangedEventArgs("显示功能按钮");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs 获取状态文本 = new PropertyChangedEventArgs("获取状态文本");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs 使用类型文本 = new PropertyChangedEventArgs("使用类型文本");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs 版本号 = new PropertyChangedEventArgs("版本号");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs 加速Cursor已启用 = new PropertyChangedEventArgs("加速Cursor已启用");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs 窗口标题 = new PropertyChangedEventArgs("窗口标题");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs IsProcessing = new PropertyChangedEventArgs("IsProcessing");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs 显示恢复购买按钮 = new PropertyChangedEventArgs("显示恢复购买按钮");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs CurrentOrderNo = new PropertyChangedEventArgs("CurrentOrderNo");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs 价格加载中 = new PropertyChangedEventArgs("价格加载中");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs 周卡原价 = new PropertyChangedEventArgs("周卡原价");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs 周卡现价 = new PropertyChangedEventArgs("周卡现价");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs 月卡原价 = new PropertyChangedEventArgs("月卡原价");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs 月卡现价 = new PropertyChangedEventArgs("月卡现价");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs 周卡数量 = new PropertyChangedEventArgs("周卡数量");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs 月卡数量 = new PropertyChangedEventArgs("月卡数量");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs 周卡总计信息 = new PropertyChangedEventArgs("周卡总计信息");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangedEventArgs 月卡总计信息 = new PropertyChangedEventArgs("月卡总计信息");
}
