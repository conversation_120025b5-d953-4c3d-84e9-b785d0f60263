using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.ComponentModel.__Internals;
using CommunityToolkit.Mvvm.Input;
using CursorPro.Models;
using CursorPro.Views;
using MsBox.Avalonia.Enums;

namespace CursorPro.ViewModels;

public class 购买卡密VM : ViewModelBase
{
	private readonly string _apiBaseUrl;

	[ObservableProperty]
	private bool _isProcessing;

	[ObservableProperty]
	private bool _显示恢复购买按钮 = true;

	private Timer? _轮询定时器;

	private string? _当前订单号;

	private int _轮询次数;

	private const int 最大轮询次数 = 100;

	private const int 轮询间隔毫秒 = 3000;

	private static 购买卡密VM? _轮询实例;

	[ObservableProperty]
	private string _currentOrderNo = string.Empty;

	[ObservableProperty]
	private bool _价格加载中;

	[ObservableProperty]
	private string _周卡原价 = "9.9";

	[ObservableProperty]
	private string _周卡现价 = "9.9";

	[ObservableProperty]
	private string _月卡原价 = "39.9";

	[ObservableProperty]
	private string _月卡现价 = "39.9";

	[ObservableProperty]
	private int _周卡数量 = 1;

	[ObservableProperty]
	private int _月卡数量 = 1;

	[ObservableProperty]
	private string _周卡总计信息 = "总计: ¥9.9 (延长7天)";

	[ObservableProperty]
	private string _月卡总计信息 = "总计: ¥29.9 (延长30天)";

	private const string 本地订单键名 = "pending_order_info";

	private static 购买卡密? _购买窗口实例;

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	private RelayCommand<string>? _调整周卡数量Command;

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	private RelayCommand<string>? _调整月卡数量Command;

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	private AsyncRelayCommand<string>? _选择卡密Command;

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	private AsyncRelayCommand? _恢复购买记录Command;

	public bool 周卡有特惠
	{
		get
		{
			if (double.TryParse(周卡现价, out var result) && double.TryParse(周卡原价, out var result2))
			{
				return result < result2;
			}
			return false;
		}
	}

	public bool 月卡有特惠
	{
		get
		{
			if (double.TryParse(月卡现价, out var result) && double.TryParse(月卡原价, out var result2))
			{
				return result < result2;
			}
			return false;
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public bool IsProcessing
	{
		get
		{
			return _isProcessing;
		}
		set
		{
			if (!EqualityComparer<bool>.Default.Equals(_isProcessing, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.IsProcessing);
				_isProcessing = value;
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.IsProcessing);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public bool 显示恢复购买按钮
	{
		get
		{
			return _显示恢复购买按钮;
		}
		set
		{
			if (!EqualityComparer<bool>.Default.Equals(_显示恢复购买按钮, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.显示恢复购买按钮);
				_显示恢复购买按钮 = value;
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.显示恢复购买按钮);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public string CurrentOrderNo
	{
		get
		{
			return _currentOrderNo;
		}
		[MemberNotNull("_currentOrderNo")]
		set
		{
			if (!EqualityComparer<string>.Default.Equals(_currentOrderNo, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.CurrentOrderNo);
				_currentOrderNo = value;
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.CurrentOrderNo);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public bool 价格加载中
	{
		get
		{
			return _价格加载中;
		}
		set
		{
			if (!EqualityComparer<bool>.Default.Equals(_价格加载中, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.价格加载中);
				_价格加载中 = value;
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.价格加载中);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public string 周卡原价
	{
		get
		{
			return _周卡原价;
		}
		[MemberNotNull("_周卡原价")]
		set
		{
			if (!EqualityComparer<string>.Default.Equals(_周卡原价, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.周卡原价);
				_周卡原价 = value;
				On周卡原价Changed(value);
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.周卡原价);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public string 周卡现价
	{
		get
		{
			return _周卡现价;
		}
		[MemberNotNull("_周卡现价")]
		set
		{
			if (!EqualityComparer<string>.Default.Equals(_周卡现价, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.周卡现价);
				_周卡现价 = value;
				On周卡现价Changed(value);
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.周卡现价);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public string 月卡原价
	{
		get
		{
			return _月卡原价;
		}
		[MemberNotNull("_月卡原价")]
		set
		{
			if (!EqualityComparer<string>.Default.Equals(_月卡原价, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.月卡原价);
				_月卡原价 = value;
				On月卡原价Changed(value);
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.月卡原价);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public string 月卡现价
	{
		get
		{
			return _月卡现价;
		}
		[MemberNotNull("_月卡现价")]
		set
		{
			if (!EqualityComparer<string>.Default.Equals(_月卡现价, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.月卡现价);
				_月卡现价 = value;
				On月卡现价Changed(value);
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.月卡现价);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public int 周卡数量
	{
		get
		{
			return _周卡数量;
		}
		set
		{
			if (!EqualityComparer<int>.Default.Equals(_周卡数量, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.周卡数量);
				_周卡数量 = value;
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.周卡数量);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public int 月卡数量
	{
		get
		{
			return _月卡数量;
		}
		set
		{
			if (!EqualityComparer<int>.Default.Equals(_月卡数量, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.月卡数量);
				_月卡数量 = value;
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.月卡数量);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public string 周卡总计信息
	{
		get
		{
			return _周卡总计信息;
		}
		[MemberNotNull("_周卡总计信息")]
		set
		{
			if (!EqualityComparer<string>.Default.Equals(_周卡总计信息, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.周卡总计信息);
				_周卡总计信息 = value;
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.周卡总计信息);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public string 月卡总计信息
	{
		get
		{
			return _月卡总计信息;
		}
		[MemberNotNull("_月卡总计信息")]
		set
		{
			if (!EqualityComparer<string>.Default.Equals(_月卡总计信息, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.月卡总计信息);
				_月卡总计信息 = value;
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.月卡总计信息);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public IRelayCommand<string> 调整周卡数量Command => _调整周卡数量Command ?? (_调整周卡数量Command = new RelayCommand<string>(调整周卡数量));

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public IRelayCommand<string> 调整月卡数量Command => _调整月卡数量Command ?? (_调整月卡数量Command = new RelayCommand<string>(调整月卡数量));

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public IAsyncRelayCommand<string> 选择卡密Command => _选择卡密Command ?? (_选择卡密Command = new AsyncRelayCommand<string>(选择卡密));

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public IAsyncRelayCommand 恢复购买记录Command => _恢复购买记录Command ?? (_恢复购买记录Command = new AsyncRelayCommand(恢复购买记录));

	public 购买卡密VM()
	{
		_apiBaseUrl = Info.API基础地址 + ":" + Info.支付服务端口;
		初始化默认价格状态();
		检查恢复购买按钮可见性();
		Task.Run(async delegate
		{
			await 加载价格();
		});
	}

	private void 初始化默认价格状态()
	{
		周卡原价 = "9.9";
		周卡现价 = "9.9";
		月卡原价 = "39.9";
		月卡现价 = "29.9";
		更新周卡总计信息();
		更新月卡总计信息();
	}

	[RelayCommand]
	private void 调整周卡数量(string 操作)
	{
		if (操作 == "增加" && 周卡数量 < 12)
		{
			周卡数量++;
		}
		else if (操作 == "减少" && 周卡数量 > 1)
		{
			周卡数量--;
		}
		更新周卡总计信息();
	}

	[RelayCommand]
	private void 调整月卡数量(string 操作)
	{
		if (操作 == "增加" && 月卡数量 < 12)
		{
			月卡数量++;
		}
		else if (操作 == "减少" && 月卡数量 > 1)
		{
			月卡数量--;
		}
		更新月卡总计信息();
	}

	private void 更新周卡总计信息()
	{
		if (decimal.TryParse(周卡现价, out var result))
		{
			decimal value = result * (decimal)周卡数量;
			int value2 = 7 * 周卡数量;
			周卡总计信息 = $"总计: ¥{value} (延长{value2}天)";
		}
	}

	private void 更新月卡总计信息()
	{
		if (decimal.TryParse(月卡现价, out var result))
		{
			decimal value = result * (decimal)月卡数量;
			int value2 = 30 * 月卡数量;
			月卡总计信息 = $"总计: ¥{value} (延长{value2}天)";
		}
	}

	private void 检查恢复购买按钮可见性()
	{
		try
		{
			string value = LocalConfig.读取("CardKey");
			显示恢复购买按钮 = string.IsNullOrEmpty(value);
		}
		catch (Exception)
		{
			显示恢复购买按钮 = true;
		}
	}

	private async Task 加载价格()
	{
		try
		{
			JsonDocument jsonDocument = await HttpService.GetAsync<JsonDocument>(Info.API基础地址 + ":" + Info.付费服务端口 + "/api/prices");
			if (jsonDocument == null)
			{
				return;
			}
			JsonElement rootElement = jsonDocument.RootElement;
			if (rootElement.TryGetProperty("success", out var value) && value.GetBoolean())
			{
				if (!rootElement.TryGetProperty("data", out var value2) || !value2.TryGetProperty("prices", out var value3))
				{
					return;
				}
				{
					foreach (JsonElement item in value3.EnumerateArray())
					{
						if (!item.TryGetProperty("type", out var value4) || !item.TryGetProperty("price", out var value5))
						{
							continue;
						}
						string text = value4.GetString() ?? "";
						double num = value5.GetDouble();
						if (!(text == "周卡"))
						{
							if (text == "月卡")
							{
								string text2 = num.ToString("F1");
								if (月卡现价 != text2)
								{
									月卡现价 = text2;
								}
							}
						}
						else
						{
							string text3 = num.ToString("F1");
							if (周卡现价 != text3)
							{
								周卡现价 = text3;
							}
						}
					}
					return;
				}
			}
			if (rootElement.TryGetProperty("message", out var value6))
			{
				value6.GetString();
			}
		}
		catch (Exception)
		{
		}
	}

	[RelayCommand]
	private async Task 选择卡密(string 类型)
	{
		_ = 6;
		try
		{
			IsProcessing = true;
			int 数量 = ((类型 == "周卡") ? 周卡数量 : 月卡数量);
			string 机器码 = (string.IsNullOrEmpty(Info.机器码) ? new 获取设备标识().获取机器码() : Info.机器码);
			if (string.IsNullOrEmpty(Info.机器码))
			{
				Info.机器码 = 机器码;
			}
			string value = LocalConfig.读取("CardKey");
			bool 是续卡 = !string.IsNullOrEmpty(value);
			JsonElement? jsonElement = ((!是续卡) ? (await 创建支付链接(类型, 机器码, 数量)) : (await 创建续费链接(类型, 机器码, 数量)));
			JsonElement? jsonElement2 = jsonElement;
			if (!jsonElement2.HasValue)
			{
				await Tools.显示消息框Async("服务器错误", "无法连接到服务器，请检查网络连接后重试。", ButtonEnum.Ok, Icon.Error);
				return;
			}
			JsonElement value2 = jsonElement2.Value;
			if (value2.TryGetProperty("success", out var value3) && value3.GetBoolean())
			{
				if (value2.TryGetProperty("order_no", out var value4))
				{
					CurrentOrderNo = value4.GetString() ?? string.Empty;
				}
				if (!value2.TryGetProperty("payment_url", out var value5))
				{
					await Tools.显示消息框Async("错误", "获取支付链接失败，请稍后重试。", ButtonEnum.Ok, Icon.Error);
					return;
				}
				string text = value5.GetString() ?? string.Empty;
				if (string.IsNullOrEmpty(text))
				{
					await Tools.显示消息框Async("错误", "支付链接无效，请稍后重试。", ButtonEnum.Ok, Icon.Error);
					return;
				}
				保存订单到本地(CurrentOrderNo, 类型, 机器码, 是续卡);
				启动订单轮询(CurrentOrderNo);
				打开浏览器(text);
				关闭购买窗口();
			}
			else
			{
				JsonElement value6;
				string text2 = (value2.TryGetProperty("message", out value6) ? (value6.GetString() ?? "创建支付链接失败") : "创建支付链接失败");
				await Tools.显示消息框Async("创建失败", "创建支付链接失败: " + text2, ButtonEnum.Ok, Icon.Error);
			}
		}
		catch (Exception ex)
		{
			_ = ex;
			await Tools.显示消息框Async("激活失败", "激活过程中出现错误，请稍后重试。", ButtonEnum.Ok, Icon.Error);
		}
		finally
		{
			IsProcessing = false;
		}
	}

	private void 保存订单到本地(string 订单号, string 卡密类型, string 机器码, bool 是续卡)
	{
		try
		{
			string 值 = JsonSerializer.Serialize(new
			{
				order_no = 订单号,
				card_type = 卡密类型,
				machine_code = 机器码,
				is_renewal = 是续卡,
				create_time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
			});
			LocalConfig.写入("pending_order_info", 值);
		}
		catch (Exception)
		{
		}
	}

	public static bool 检查本地订单()
	{
		try
		{
			string text = LocalConfig.读取("pending_order_info");
			if (string.IsNullOrEmpty(text))
			{
				return false;
			}
			if (检查订单是否超时(text))
			{
				删除本地订单();
				return false;
			}
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	private static bool 检查订单是否超时(string 订单Json)
	{
		try
		{
			if (JsonDocument.Parse(订单Json).RootElement.TryGetProperty("create_time", out var value))
			{
				string text = value.GetString();
				if (!string.IsNullOrEmpty(text) && DateTime.TryParse(text, out var result))
				{
					return (DateTime.Now - result).TotalMinutes > 120.0;
				}
			}
			return false;
		}
		catch (Exception)
		{
			return false;
		}
	}

	public static JsonElement? 获取本地订单()
	{
		try
		{
			string text = LocalConfig.读取("pending_order_info");
			if (!string.IsNullOrEmpty(text))
			{
				return JsonDocument.Parse(text).RootElement.Clone();
			}
			return null;
		}
		catch (Exception)
		{
			return null;
		}
	}

	public static void 删除本地订单()
	{
		try
		{
			LocalConfig.写入("pending_order_info", "");
		}
		catch (Exception)
		{
		}
	}

	public async Task<验证结果?> 检查并处理本地订单()
	{
		_ = 1;
		try
		{
			JsonElement? jsonElement = 获取本地订单();
			if (!jsonElement.HasValue)
			{
				return null;
			}
			JsonElement value = jsonElement.Value;
			string text = string.Empty;
			if (value.TryGetProperty("order_no", out var value2))
			{
				text = value2.GetString() ?? string.Empty;
			}
			if (string.IsNullOrEmpty(text))
			{
				return null;
			}
			try
			{
				JsonElement? jsonElement2 = await 检测订单状态(text);
				if (jsonElement2.HasValue)
				{
					JsonElement value3 = jsonElement2.Value;
					JsonElement value9;
					if (value3.TryGetProperty("success", out var value4) && value4.GetBoolean())
					{
						if (value3.TryGetProperty("order_info", out var value5))
						{
							bool flag = false;
							if (value5.TryGetProperty("支付时间", out var value6))
							{
								flag = !string.IsNullOrEmpty(value6.GetString());
							}
							if (flag)
							{
								string text2 = string.Empty;
								_ = string.Empty;
								if (value5.TryGetProperty("卡密", out var value7))
								{
									text2 = value7.GetString() ?? string.Empty;
								}
								if (value5.TryGetProperty("有效期", out var value8) && value8.GetString() == null)
								{
									_ = string.Empty;
								}
								if (!string.IsNullOrEmpty(text2))
								{
									LocalConfig.写入("CardKey", text2);
									删除本地订单();
									await Tools.显示消息框Async("激活成功", "感谢您的支持！已成功解锁独享无限额度权益", ButtonEnum.Ok, Icon.Success);
									return new 验证结果
									{
										Success = true,
										Message = "激活成功! 和cursor继续对话吧"
									};
								}
							}
						}
					}
					else if (value3.TryGetProperty("message", out value9))
					{
						value9.GetString();
					}
				}
			}
			catch (Exception)
			{
			}
		}
		catch (Exception)
		{
		}
		return null;
	}

	private async Task<JsonElement?> 创建支付链接(string 卡密类型, string 机器码, int 数量 = 1)
	{
		JsonElement? result = default(JsonElement?);
		object obj;
		int num;
		try
		{
			var data = new
			{
				card_type = 卡密类型,
				machine_code = 机器码,
				quantity = 数量,
				app_name = Info.AppName,
				remark = Info.代理
			};
			JsonDocument jsonDocument = await HttpService.PostAsync<object, JsonDocument>(_apiBaseUrl + "/create_payment", data);
			if (jsonDocument != null)
			{
				result = jsonDocument.RootElement.Clone();
				return result;
			}
			result = null;
			return result;
		}
		catch (Exception ex)
		{
			obj = ex;
			num = 1;
		}
		if (num != 1)
		{
			return result;
		}
		_ = (Exception)obj;
		await Tools.显示消息框Async("连接错误", "创建激活链接失败，请检查网络连接后重试。", ButtonEnum.Ok, Icon.Error);
		return null;
	}

	public async Task<JsonElement?> 检测订单状态(string 订单号)
	{
		try
		{
			var data = new
			{
				order_no = 订单号
			};
			return (await HttpService.PostAsync<object, JsonDocument>(_apiBaseUrl + "/query_order", data))?.RootElement.Clone();
		}
		catch (Exception)
		{
			throw;
		}
	}

	private void 打开浏览器(string url)
	{
		Tools.OpenUrl(url);
	}

	public static void 显示购买窗口()
	{
		try
		{
			Dispatcher.UIThread.Post(delegate
			{
				try
				{
					if (_购买窗口实例 != null)
					{
						_购买窗口实例.Activate();
						_购买窗口实例.Topmost = true;
						_购买窗口实例.Topmost = false;
					}
					else
					{
						_购买窗口实例 = new 购买卡密();
						_购买窗口实例.Closed += delegate
						{
							_购买窗口实例 = null;
						};
						_购买窗口实例.Show();
					}
				}
				catch (Exception)
				{
					_购买窗口实例 = null;
				}
			});
		}
		catch (Exception)
		{
		}
	}

	public static void 关闭购买窗口()
	{
		try
		{
			if (_购买窗口实例 != null)
			{
				_购买窗口实例.Close();
				_购买窗口实例 = null;
			}
		}
		catch (Exception)
		{
			_购买窗口实例 = null;
		}
	}

	private async Task<JsonElement?> 创建续费链接(string 卡密类型, string 机器码, int 数量 = 1)
	{
		JsonElement? result = default(JsonElement?);
		object obj;
		int num;
		try
		{
			string text = LocalConfig.读取("CardKey");
			if (!string.IsNullOrEmpty(text))
			{
				var data = new
				{
					card_key = text,
					card_type = 卡密类型,
					machine_code = 机器码,
					quantity = 数量,
					app_name = Info.AppName,
					remark = Info.代理
				};
				JsonDocument jsonDocument = await HttpService.PostAsync<object, JsonDocument>(_apiBaseUrl + "/create_renewal_payment", data);
				if (jsonDocument != null)
				{
					JsonElement rootElement = jsonDocument.RootElement;
					if (rootElement.TryGetProperty("success", out var value) && !value.GetBoolean())
					{
						rootElement.TryGetProperty("message", out var _);
					}
					result = rootElement.Clone();
					return result;
				}
				result = null;
				return result;
			}
			await Tools.显示消息框Async("续期失败", "无法找到当前使用的激活信息，请重新登录后再试。", ButtonEnum.Ok, Icon.Error);
			result = null;
			return result;
		}
		catch (Exception ex)
		{
			obj = ex;
			num = 1;
		}
		if (num != 1)
		{
			return result;
		}
		_ = (Exception)obj;
		await Tools.显示消息框Async("续期错误", "创建续期链接失败，请检查网络连接后重试。", ButtonEnum.Ok, Icon.Error);
		return null;
	}

	[RelayCommand]
	private async Task 恢复购买记录()
	{
		_ = 9;
		try
		{
			if (IsProcessing || await Tools.显示确认消息框Async("恢复购买记录", "将根据当前设备信息查找已购买的服务记录，是否继续？", ButtonEnum.YesNo, Icon.Question) != ButtonResult.Yes)
			{
				return;
			}
			IsProcessing = true;
			string text = new 获取设备标识().获取机器码();
			if (string.IsNullOrEmpty(text))
			{
				await Tools.显示消息框Async("错误", "无法获取设备标识信息", ButtonEnum.Ok, Icon.Error);
				return;
			}
			var data = new
			{
				machine_code = text
			};
			JsonDocument jsonDocument = await HttpService.PostAsync<object, JsonDocument>(Info.API基础地址 + ":" + Info.付费服务端口 + "/api/restore_purchase", data);
			if (jsonDocument == null)
			{
				await Tools.显示消息框Async("错误", "服务器响应异常", ButtonEnum.Ok, Icon.Error);
				return;
			}
			JsonElement rootElement = jsonDocument.RootElement;
			if (rootElement.TryGetProperty("success", out var value) && value.GetBoolean())
			{
				string text2 = "";
				string text3 = "";
				if (rootElement.TryGetProperty("card_key", out var value2))
				{
					text2 = value2.GetString() ?? "";
				}
				if (rootElement.TryGetProperty("expiry_time", out var value3))
				{
					text3 = value3.GetString() ?? "";
				}
				if (string.IsNullOrEmpty(text2))
				{
					await Tools.显示消息框Async("错误", "服务器返回数据异常", ButtonEnum.Ok, Icon.Error);
					return;
				}
				LocalConfig.写入("CardKey", text2);
				if (!string.IsNullOrEmpty(text3))
				{
					LocalConfig.写入("ExpiryTime", text3);
					Info.到期时间 = text3;
				}
				await Tools.显示消息框Async("成功", "购买记录恢复成功！", ButtonEnum.Ok, Icon.Success);
				通知主窗口刷新状态();
				关闭窗口();
			}
			else
			{
				string text4 = "恢复购买记录失败";
				if (rootElement.TryGetProperty("message", out var value4))
				{
					text4 = value4.GetString() ?? text4;
				}
				if (!text4.Contains("未找到") && !text4.Contains("不存在") && !text4.Contains("没有找到") && !text4.Contains("无记录"))
				{
					await Tools.显示消息框Async("提示", text4);
				}
				else if (await Tools.显示确认消息框Async("未找到购买记录", "系统未找到您的购买记录，您可以手动输入卡密来恢复。是否现在输入？", ButtonEnum.YesNo, Icon.Question) == ButtonResult.Yes)
				{
					await 显示卡密输入对话框();
				}
			}
		}
		catch (Exception ex)
		{
			_ = ex;
			await Tools.显示消息框Async("错误", "网络连接失败，请稍后重试", ButtonEnum.Ok, Icon.Error);
		}
		finally
		{
			IsProcessing = false;
		}
	}

	private void 通知主窗口刷新状态()
	{
		try
		{
			if (!(Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime classicDesktopStyleApplicationLifetime))
			{
				return;
			}
			object obj = classicDesktopStyleApplicationLifetime.MainWindow?.DataContext;
			主要窗口 mainViewModel = obj as 主要窗口;
			if (mainViewModel != null)
			{
				Dispatcher.UIThread.Post(delegate
				{
					mainViewModel.更新使用类型文本();
				});
			}
		}
		catch (Exception)
		{
		}
	}

	private void 关闭窗口()
	{
		try
		{
			if (_购买窗口实例 != null)
			{
				Dispatcher.UIThread.Post(delegate
				{
					_购买窗口实例.Close();
					_购买窗口实例 = null;
				});
			}
		}
		catch (Exception)
		{
		}
	}

	private void 启动订单轮询(string 订单号)
	{
		try
		{
			停止轮询();
			_轮询实例 = this;
			_当前订单号 = 订单号;
			_轮询次数 = 0;
			_轮询定时器 = new Timer(轮询回调, null, 3000, 3000);
		}
		catch (Exception)
		{
		}
	}

	private static async void 轮询回调(object? state)
	{
		try
		{
			if (_轮询实例 != null)
			{
				await _轮询实例.执行轮询逻辑();
			}
		}
		catch (Exception)
		{
		}
	}

	private async Task 执行轮询逻辑()
	{
		_ = 1;
		try
		{
			_轮询次数++;
			if (_轮询次数 > 100)
			{
				停止轮询();
				return;
			}
			if (string.IsNullOrEmpty(_当前订单号))
			{
				停止轮询();
				return;
			}
			JsonElement? jsonElement = await 检测订单状态(_当前订单号);
			if (jsonElement.HasValue)
			{
				await 处理轮询结果(jsonElement.Value);
			}
		}
		catch (Exception)
		{
		}
	}

	private async Task 处理轮询结果(JsonElement 订单结果)
	{
		try
		{
			if (!订单结果.TryGetProperty("success", out var value) || !value.GetBoolean() || !订单结果.TryGetProperty("order_info", out var value2))
			{
				return;
			}
			停止轮询();
			if (value2.ValueKind == JsonValueKind.Object && value2.TryGetProperty("卡密", out var value3))
			{
				string text = value3.GetString() ?? "";
				if (!string.IsNullOrEmpty(text))
				{
					await 自动激活卡密(text);
				}
			}
		}
		catch (Exception)
		{
		}
	}

	private async Task 自动激活卡密(string 卡密)
	{
		_ = 1;
		try
		{
			验证结果 验证结果2 = await 调用激活API(卡密);
			if (验证结果2 != null && 验证结果2.Success)
			{
				LocalConfig.写入("CardKey", 卡密);
				if (!string.IsNullOrEmpty(验证结果2.ExpiryTime))
				{
					LocalConfig.写入("ExpiryTime", 验证结果2.ExpiryTime);
					Info.到期时间 = 验证结果2.ExpiryTime;
				}
				await Dispatcher.UIThread.InvokeAsync(async delegate
				{
					通知主窗口刷新状态();
					await Tools.显示消息框Async("激活成功", "您的服务已自动激活！", ButtonEnum.Ok, Icon.Success);
					关闭购买窗口();
				});
			}
		}
		catch (Exception)
		{
		}
	}

	private async Task<验证结果?> 调用激活API(string 卡密)
	{
		try
		{
			return await new 检查限制().验证付费账号(卡密);
		}
		catch (Exception)
		{
			return null;
		}
	}

	public void 停止轮询()
	{
		try
		{
			if (_轮询定时器 != null)
			{
				_轮询定时器.Dispose();
				_轮询定时器 = null;
			}
			_当前订单号 = null;
			_轮询次数 = 0;
		}
		catch (Exception)
		{
		}
	}

	private async Task 显示卡密输入对话框()
	{
		try
		{
			string 输入的卡密 = await Tools.显示输入框Async("提示", "请输入您的秘钥：");
			if (string.IsNullOrWhiteSpace(输入的卡密))
			{
				return;
			}
			验证结果 验证结果2 = await new 检查限制().验证付费账号(输入的卡密.Trim());
			if (验证结果2 != null && 验证结果2.Success)
			{
				LocalConfig.写入("CardKey", 输入的卡密.Trim());
				if (!string.IsNullOrEmpty(验证结果2.ExpiryTime))
				{
					LocalConfig.写入("ExpiryTime", 验证结果2.ExpiryTime);
					Info.到期时间 = 验证结果2.ExpiryTime;
				}
				await Tools.显示消息框Async("验证成功", "卡密验证成功！", ButtonEnum.Ok, Icon.Success);
				通知主窗口刷新状态();
				关闭窗口();
			}
			else
			{
				string 内容 = 验证结果2?.Message ?? "卡密验证失败，请检查卡密是否正确";
				await Tools.显示消息框Async("验证失败", 内容, ButtonEnum.Ok, Icon.Warning);
			}
		}
		catch (Exception ex)
		{
			_ = ex;
			await Tools.显示消息框Async("错误", "验证过程中出错，请稍后重试", ButtonEnum.Ok, Icon.Error);
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	private void On周卡原价Changed(string value)
	{
		OnPropertyChanged("周卡有特惠");
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	private void On周卡现价Changed(string value)
	{
		OnPropertyChanged("周卡有特惠");
		更新周卡总计信息();
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	private void On月卡原价Changed(string value)
	{
		OnPropertyChanged("月卡有特惠");
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	private void On月卡现价Changed(string value)
	{
		OnPropertyChanged("月卡有特惠");
		更新月卡总计信息();
	}
}
