<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <AssemblyName>CursorPro</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <TargetFramework>netcoreapp8.0</TargetFramework>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup>
    <LangVersion>12.0</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>app.ico</ApplicationIcon>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <RootNamespace />
  </PropertyGroup>
  <ItemGroup>
    <None Remove="-AvaloniaResources" />
    <EmbeddedResource Include="-AvaloniaResources" LogicalName="!AvaloniaResources" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Avalonia" Version="11.0.5" />
    <PackageReference Include="Avalonia.Desktop" Version="11.0.5" />
    <PackageReference Include="Avalonia.Themes.Fluent" Version="11.0.5" />
    <PackageReference Include="Avalonia.Fonts.Inter" Version="11.0.5" />

    <PackageReference Include="MessageBox.Avalonia" Version="3.1.5.1" />
    <PackageReference Include="Microsoft.Data.Sqlite" Version="7.0.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.0">
      <ExcludeAssets>analyzers</ExcludeAssets>
    </PackageReference>
  </ItemGroup>
</Project>