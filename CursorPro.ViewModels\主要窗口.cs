using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Avalonia;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.ComponentModel.__Internals;
using CommunityToolkit.Mvvm.Input;
using CursorPro.Models;
using Microsoft.Data.Sqlite;
using MsBox.Avalonia.Enums;

namespace CursorPro.ViewModels;

public class 主要窗口 : ViewModelBase, IDisposable
{
	private readonly 检查限制 _检查限制 = new 检查限制();

	private readonly 重置机器码 _重置机器码 = new 重置机器码();

	[ObservableProperty]
	private bool _显示功能按钮 = true;

	[ObservableProperty]
	private string _获取状态文本 = string.Empty;

	[ObservableProperty]
	private string _使用类型文本 = string.Empty;

	[ObservableProperty]
	private string _版本号 = "v" + Info.本地版本号;

	[ObservableProperty]
	private bool _加速Cursor已启用;

	private bool _显示加速Cursor功能;

	[ObservableProperty]
	private string _窗口标题 = string.Empty;

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	private AsyncRelayCommand? _一键获取快速额度Command;

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	private RelayCommand? _使用教程Command;

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	private RelayCommand? _版本链接Command;

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	private RelayCommand? _状态文本点击Command;

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	private AsyncRelayCommand? _加速Cursor切换Command;

	public bool 显示加速Cursor功能
	{
		get
		{
			return _显示加速Cursor功能;
		}
		set
		{
			if (SetProperty(ref _显示加速Cursor功能, value, "显示加速Cursor功能"))
			{
				OnPropertyChanged("版本链接边距");
			}
		}
	}

	public Thickness 版本链接边距
	{
		get
		{
			if (!显示加速Cursor功能)
			{
				return new Thickness(0.0, 17.0, 0.0, 0.0);
			}
			return new Thickness(0.0, -8.0, 0.0, 0.0);
		}
	}

	private bool 是付费用户未到期
	{
		get
		{
			DateTime? dateTime = 获取到期时间();
			if (dateTime.HasValue)
			{
				return dateTime.Value > DateTime.Now;
			}
			return false;
		}
	}

	private bool 是付费用户已到期
	{
		get
		{
			DateTime? dateTime = 获取到期时间();
			if (dateTime.HasValue)
			{
				return dateTime.Value <= DateTime.Now;
			}
			return false;
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public bool 显示功能按钮
	{
		get
		{
			return _显示功能按钮;
		}
		set
		{
			if (!EqualityComparer<bool>.Default.Equals(_显示功能按钮, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.显示功能按钮);
				_显示功能按钮 = value;
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.显示功能按钮);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public string 获取状态文本
	{
		get
		{
			return _获取状态文本;
		}
		[MemberNotNull("_获取状态文本")]
		set
		{
			if (!EqualityComparer<string>.Default.Equals(_获取状态文本, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.获取状态文本);
				_获取状态文本 = value;
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.获取状态文本);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public string 使用类型文本
	{
		get
		{
			return _使用类型文本;
		}
		[MemberNotNull("_使用类型文本")]
		set
		{
			if (!EqualityComparer<string>.Default.Equals(_使用类型文本, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.使用类型文本);
				_使用类型文本 = value;
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.使用类型文本);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public string 版本号
	{
		get
		{
			return _版本号;
		}
		[MemberNotNull("_版本号")]
		set
		{
			if (!EqualityComparer<string>.Default.Equals(_版本号, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.版本号);
				_版本号 = value;
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.版本号);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public bool 加速Cursor已启用
	{
		get
		{
			return _加速Cursor已启用;
		}
		set
		{
			if (!EqualityComparer<bool>.Default.Equals(_加速Cursor已启用, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.加速Cursor已启用);
				_加速Cursor已启用 = value;
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.加速Cursor已启用);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public string 窗口标题
	{
		get
		{
			return _窗口标题;
		}
		[MemberNotNull("_窗口标题")]
		set
		{
			if (!EqualityComparer<string>.Default.Equals(_窗口标题, value))
			{
				OnPropertyChanging(__KnownINotifyPropertyChangingArgs.窗口标题);
				_窗口标题 = value;
				OnPropertyChanged(__KnownINotifyPropertyChangedArgs.窗口标题);
			}
		}
	}

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public IAsyncRelayCommand 一键获取快速额度Command => _一键获取快速额度Command ?? (_一键获取快速额度Command = new AsyncRelayCommand(一键获取快速额度Async));

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public IRelayCommand 使用教程Command => _使用教程Command ?? (_使用教程Command = new RelayCommand(使用教程));

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public IRelayCommand 版本链接Command => _版本链接Command ?? (_版本链接Command = new RelayCommand(版本链接));

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public IRelayCommand 状态文本点击Command => _状态文本点击Command ?? (_状态文本点击Command = new RelayCommand(状态文本点击));

	[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.2.0.0")]
	[ExcludeFromCodeCoverage]
	public IAsyncRelayCommand 加速Cursor切换Command => _加速Cursor切换Command ?? (_加速Cursor切换Command = new AsyncRelayCommand(加速Cursor切换Async));

	private DateTime? 获取到期时间()
	{
		string text = LocalConfig.读取("ExpiryTime");
		if (!string.IsNullOrEmpty(text) && DateTime.TryParse(text, out var result))
		{
			return result;
		}
		return null;
	}

	public 主要窗口()
	{
		窗口标题 = string.Empty;
		CursorProcessManager.InitializeCursorPathCache();
		初始化加速Cursor状态();
		更新使用类型文本();
		更新获取状态文本();
	}

	public void 更新使用类型文本()
	{
		try
		{
			DateTime? dateTime = 获取到期时间();
			if (dateTime.HasValue)
			{
				if (dateTime.Value > DateTime.Now)
				{
					int num = (int)Math.Ceiling((dateTime.Value - DateTime.Now).TotalDays);
					if (num < 3)
					{
						使用类型文本 = $"独享不足{num}天，是否续费？";
					}
					else
					{
						使用类型文本 = $"独享剩余 {num} 天";
					}
				}
				else
				{
					使用类型文本 = "独享已到期，是否续费？";
				}
			}
			else
			{
				使用类型文本 = "免费使用中";
			}
		}
		catch (Exception)
		{
			使用类型文本 = "免费使用中";
		}
	}

	private void 更新获取状态文本()
	{
		获取状态文本 = "";
	}

	[RelayCommand]
	private async Task 一键获取快速额度Async()
	{
		_ = 8;
		try
		{
			更新使用类型文本();
			获取状态文本 = string.Empty;
			if (string.IsNullOrEmpty(CursorProcessManager.GetCachedCursorPath()))
			{
				获取状态文本 = "获取失败";
				await Tools.显示消息框Async("提示", "请先启动Cursor再获取", ButtonEnum.Ok, Icon.Warning);
				return;
			}
			验证结果 验证结果2 = await _检查限制.启动检查();
			if (!验证结果2.Success)
			{
				if (是否免费限制问题(验证结果2.Message))
				{
					_重置机器码.DeleteProxySettings();
					if (await Tools.显示确认消息框Async("温馨提示", 验证结果2.Message, ButtonEnum.YesNo) == ButtonResult.Yes)
					{
						购买卡密VM.显示购买窗口();
					}
				}
				else
				{
					await Tools.显示消息框Async("温馨提示", 验证结果2.Message, ButtonEnum.Ok, Icon.Warning);
				}
				return;
			}
			if (验证结果2.IsFake == true)
			{
				if (!免费虚假获取.是否为免费用户())
				{
					await 免费虚假获取.执行付费虚假获取(delegate(string text)
					{
						获取状态文本 = text;
					});
				}
				else
				{
					await 免费虚假获取.执行免费虚假获取(delegate(string text)
					{
						获取状态文本 = text;
					});
				}
			}
			else if (验证结果2.AccountInfo != null)
			{
				string 邮箱 = 验证结果2.AccountInfo.邮箱;
				string 访问令牌 = 验证结果2.AccountInfo.访问令牌;
				string 刷新令牌 = 验证结果2.AccountInfo.刷新令牌;
				string proxyServer = 验证结果2.ProxyServer;
				string.IsNullOrEmpty(proxyServer);
				string proxyServer2 = (加速Cursor已启用 ? proxyServer : "");
				if (await _重置机器码.执行重置机器码(邮箱, 访问令牌, 刷新令牌, proxyServer2))
				{
					获取状态文本 = "获取成功! 和cursor继续对话吧";
					显示加速Cursor功能 = true;
				}
			}
			else
			{
				获取状态文本 = "获取失败";
				await Tools.显示消息框Async("提示", "无法获取账号信息，请稍后重试", ButtonEnum.Ok, Icon.Warning);
			}
		}
		catch (Exception ex)
		{
			_ = ex;
			获取状态文本 = "获取失败";
			await Tools.显示消息框Async("提示", "获取额度失败，请稍后重试", ButtonEnum.Ok, Icon.Warning);
		}
		finally
		{
			SqliteConnection.ClearAllPools();
			更新使用类型文本();
		}
	}

	private bool 是否免费限制问题(string 错误消息)
	{
		if (string.IsNullOrEmpty(错误消息))
		{
			return false;
		}
		string[] array = new string[10] { "到期", "过期", "紧张", "上限", "重新购买", "次数", "限额", "共享", "不足", "用完" };
		foreach (string value in array)
		{
			if (错误消息.Contains(value, StringComparison.OrdinalIgnoreCase))
			{
				return true;
			}
		}
		return false;
	}

	[RelayCommand]
	private void 使用教程()
	{
		Tools.OpenUrl(Info.使用教程链接);
	}

	[RelayCommand]
	private void 版本链接()
	{
		Tools.OpenUrl(Info.获取下载链接());
	}

	[RelayCommand]
	private void 状态文本点击()
	{
		try
		{
			购买卡密VM.显示购买窗口();
		}
		catch (Exception)
		{
		}
	}

	private async void 初始化加速Cursor状态()
	{
		try
		{
			if (显示加速Cursor功能 = !string.IsNullOrEmpty(LocalConfig.读取("CardKey")))
			{
				string value = LocalConfig.读取("AccelerateCursor");
				bool result = default(bool);
				if (!string.IsNullOrEmpty(value) && bool.TryParse(value, out result) && result)
				{
					await 验证并应用加速状态Async(静默模式: true);
				}
				else
				{
					加速Cursor已启用 = false;
				}
			}
			else
			{
				加速Cursor已启用 = false;
			}
		}
		catch (Exception)
		{
			显示加速Cursor功能 = false;
			加速Cursor已启用 = false;
		}
	}

	private async Task 验证并应用加速状态Async(bool 静默模式 = false)
	{
		try
		{
			string text = LocalConfig.读取("CardKey");
			if (string.IsNullOrEmpty(text))
			{
				加速Cursor已启用 = false;
				LocalConfig.写入("AccelerateCursor", "false");
				return;
			}
			(bool success, string message, string proxyServer, string? expiryTime) 验证结果2 = await 调用加速权限验证API(text);
			if (验证结果2.success)
			{
				if (await 应用加速配置Async(验证结果2.proxyServer))
				{
					加速Cursor已启用 = true;
					LocalConfig.写入("AccelerateCursor", "true");
					if (!string.IsNullOrEmpty(验证结果2.expiryTime))
					{
						更新使用类型文本();
					}
				}
				else
				{
					加速Cursor已启用 = false;
					LocalConfig.写入("AccelerateCursor", "false");
					if (!静默模式)
					{
						await Tools.显示消息框Async("错误", "应用加速配置失败", ButtonEnum.Ok, Icon.Error);
					}
				}
			}
			else
			{
				加速Cursor已启用 = false;
				LocalConfig.写入("AccelerateCursor", "false");
				清理加速配置();
				if (!静默模式)
				{
					await 处理权限验证失败(验证结果2.message);
				}
			}
		}
		catch (Exception ex)
		{
			_ = ex;
			加速Cursor已启用 = false;
			LocalConfig.写入("AccelerateCursor", "false");
			if (!静默模式)
			{
				await Tools.显示消息框Async("错误", "网络连接错误，请稍后重试", ButtonEnum.Ok, Icon.Error);
			}
		}
	}

	[RelayCommand]
	private async Task 加速Cursor切换Async()
	{
		try
		{
			if (加速Cursor已启用)
			{
				await 验证并应用加速状态Async();
				return;
			}
			清理加速配置();
			LocalConfig.写入("AccelerateCursor", "false");
		}
		catch (Exception ex)
		{
			_ = ex;
			await Tools.显示消息框Async("错误", "切换加速设置时发生错误", ButtonEnum.Ok, Icon.Error);
		}
	}

	private async Task<(bool success, string message, string proxyServer, string? expiryTime)> 调用加速权限验证API(string 卡密)
	{
		_ = 1;
		try
		{
			using HttpClient client = new HttpClient();
			client.Timeout = TimeSpan.FromSeconds(10.0);
			StringContent content = new StringContent(JsonSerializer.Serialize(new
			{
				key = 卡密
			}), Encoding.UTF8, "application/json");
			string requestUri = Info.API基础地址 + ":" + Info.付费服务端口 + "/api/get_proxy";
			JsonElement jsonElement = JsonSerializer.Deserialize<JsonElement>(await (await client.PostAsync(requestUri, content)).Content.ReadAsStringAsync());
			if (jsonElement.TryGetProperty("success", out var value) && value.GetBoolean())
			{
				string item = "";
				if (jsonElement.TryGetProperty("proxy_server", out var value2))
				{
					item = value2.GetString() ?? "";
				}
				string text = null;
				if (jsonElement.TryGetProperty("key_expiry_time", out var value3))
				{
					text = value3.GetString();
					if (!string.IsNullOrEmpty(text))
					{
						LocalConfig.写入("ExpiryTime", text);
					}
				}
				return (success: true, message: "成功", proxyServer: item, expiryTime: text);
			}
			string item2 = "未知错误";
			if (jsonElement.TryGetProperty("message", out var value4))
			{
				item2 = value4.GetString() ?? "未知错误";
			}
			return (success: false, message: item2, proxyServer: "", expiryTime: null);
		}
		catch (Exception)
		{
			return (success: false, message: "网络连接失败", proxyServer: "", expiryTime: null);
		}
	}

	private Task<bool> 应用加速配置Async(string proxyServer)
	{
		try
		{
			if (string.IsNullOrEmpty(proxyServer))
			{
				return Task.FromResult(result: false);
			}
			return Task.FromResult(_重置机器码.ModifySettingsJson(proxyServer));
		}
		catch (Exception)
		{
			return Task.FromResult(result: false);
		}
	}

	private void 清理加速配置()
	{
		try
		{
			_重置机器码.DeleteProxySettings();
		}
		catch (Exception)
		{
		}
	}

	private async Task 处理权限验证失败(string errorMessage)
	{
		_ = 2;
		try
		{
			if (errorMessage.Contains("购买后") || errorMessage.Contains("已过期"))
			{
				if (await Tools.显示确认消息框Async("提示", errorMessage, ButtonEnum.YesNo) == ButtonResult.Yes)
				{
					await 调用购买APIAsync();
				}
			}
			else
			{
				await Tools.显示消息框Async("提示", errorMessage);
			}
		}
		catch (Exception)
		{
		}
	}

	private async Task 调用购买APIAsync()
	{
		try
		{
			string text = LocalConfig.读取("CardKey");
			if (string.IsNullOrEmpty(text))
			{
				await Tools.显示消息框Async("错误", "卡密信息缺失", ButtonEnum.Ok, Icon.Error);
				return;
			}
			using HttpClient client = new HttpClient();
			client.Timeout = TimeSpan.FromSeconds(10.0);
			StringContent content = new StringContent(JsonSerializer.Serialize(new
			{
				card_key = text
			}), Encoding.UTF8, "application/json");
			string requestUri = Info.API基础地址 + ":" + Info.支付服务端口 + "/create_proxy_payment";
			JsonElement jsonElement = JsonSerializer.Deserialize<JsonElement>(await (await client.PostAsync(requestUri, content)).Content.ReadAsStringAsync());
			if (jsonElement.TryGetProperty("success", out var value) && value.GetBoolean())
			{
				if (jsonElement.TryGetProperty("payment_url", out var value2))
				{
					string text2 = value2.GetString() ?? "";
					if (!string.IsNullOrEmpty(text2))
					{
						Process.Start(new ProcessStartInfo
						{
							FileName = text2,
							UseShellExecute = true
						});
					}
				}
			}
			else
			{
				string 内容 = "购买失败";
				if (jsonElement.TryGetProperty("message", out var value3))
				{
					内容 = value3.GetString() ?? "购买失败";
				}
				await Tools.显示消息框Async("错误", 内容, ButtonEnum.Ok, Icon.Error);
			}
		}
		catch (Exception ex)
		{
			_ = ex;
			await Tools.显示消息框Async("错误", "网络连接失败，请稍后重试", ButtonEnum.Ok, Icon.Error);
		}
	}

	public void Dispose()
	{
		try
		{
			try
			{
				购买卡密VM.关闭购买窗口();
			}
			catch (Exception)
			{
			}
		}
		catch (Exception)
		{
		}
	}
}
