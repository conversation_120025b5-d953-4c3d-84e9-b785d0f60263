using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;

namespace CommunityToolkit.Mvvm.ComponentModel.__Internals;

[GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.2.0.0")]
[DebuggerNonUserCode]
[ExcludeFromCodeCoverage]
[EditorBrowsable(EditorBrowsableState.Never)]
[Obsolete("This type is not intended to be used directly by user code")]
internal static class __KnownINotifyPropertyChangingArgs
{
	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs 显示功能按钮 = new PropertyChangingEventArgs("显示功能按钮");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs 获取状态文本 = new PropertyChangingEventArgs("获取状态文本");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs 使用类型文本 = new PropertyChangingEventArgs("使用类型文本");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs 版本号 = new PropertyChangingEventArgs("版本号");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs 加速Cursor已启用 = new PropertyChangingEventArgs("加速Cursor已启用");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs 窗口标题 = new PropertyChangingEventArgs("窗口标题");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs IsProcessing = new PropertyChangingEventArgs("IsProcessing");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs 显示恢复购买按钮 = new PropertyChangingEventArgs("显示恢复购买按钮");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs CurrentOrderNo = new PropertyChangingEventArgs("CurrentOrderNo");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs 价格加载中 = new PropertyChangingEventArgs("价格加载中");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs 周卡原价 = new PropertyChangingEventArgs("周卡原价");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs 周卡现价 = new PropertyChangingEventArgs("周卡现价");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs 月卡原价 = new PropertyChangingEventArgs("月卡原价");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs 月卡现价 = new PropertyChangingEventArgs("月卡现价");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs 周卡数量 = new PropertyChangingEventArgs("周卡数量");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs 月卡数量 = new PropertyChangingEventArgs("月卡数量");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs 周卡总计信息 = new PropertyChangingEventArgs("周卡总计信息");

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This field is not intended to be referenced directly by user code")]
	public static readonly PropertyChangingEventArgs 月卡总计信息 = new PropertyChangingEventArgs("月卡总计信息");
}
