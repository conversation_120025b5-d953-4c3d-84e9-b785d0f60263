<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <AssemblyName>CursorPro</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <TargetFramework>netcoreapp8.0</TargetFramework>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup>
    <LangVersion>12.0</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>app.ico</ApplicationIcon>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <RootNamespace />
  </PropertyGroup>
  <ItemGroup>
    <None Remove="-AvaloniaResources" />
    <EmbeddedResource Include="-AvaloniaResources" LogicalName="!AvaloniaResources" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Avalonia.Controls">
      <HintPath>C:\Users\<USER>\AppData\Local\Temp\.net\CursorPro\q5SbC42L0TIT20ls1d54gL9BqQUflQ4=\Avalonia.Controls.dll</HintPath>
    </Reference>
    <Reference Include="Avalonia.Base">
      <HintPath>C:\Users\<USER>\AppData\Local\Temp\.net\CursorPro\q5SbC42L0TIT20ls1d54gL9BqQUflQ4=\Avalonia.Base.dll</HintPath>
    </Reference>
    <Reference Include="CommunityToolkit.Mvvm">
      <HintPath>C:\Users\<USER>\AppData\Local\Temp\.net\CursorPro\q5SbC42L0TIT20ls1d54gL9BqQUflQ4=\CommunityToolkit.Mvvm.dll</HintPath>
    </Reference>
    <Reference Include="MsBox.Avalonia">
      <HintPath>C:\Users\<USER>\AppData\Local\Temp\.net\CursorPro\q5SbC42L0TIT20ls1d54gL9BqQUflQ4=\MsBox.Avalonia.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Data.Sqlite">
      <HintPath>C:\Users\<USER>\AppData\Local\Temp\.net\CursorPro\q5SbC42L0TIT20ls1d54gL9BqQUflQ4=\Microsoft.Data.Sqlite.dll</HintPath>
    </Reference>
    <Reference Include="Avalonia.Markup.Xaml">
      <HintPath>C:\Users\<USER>\AppData\Local\Temp\.net\CursorPro\q5SbC42L0TIT20ls1d54gL9BqQUflQ4=\Avalonia.Markup.Xaml.dll</HintPath>
    </Reference>
    <Reference Include="Avalonia.Desktop">
      <HintPath>C:\Users\<USER>\AppData\Local\Temp\.net\CursorPro\q5SbC42L0TIT20ls1d54gL9BqQUflQ4=\Avalonia.Desktop.dll</HintPath>
    </Reference>
    <Reference Include="Avalonia.Fonts.Inter">
      <HintPath>C:\Users\<USER>\AppData\Local\Temp\.net\CursorPro\q5SbC42L0TIT20ls1d54gL9BqQUflQ4=\Avalonia.Fonts.Inter.dll</HintPath>
    </Reference>
    <Reference Include="Avalonia.Themes.Fluent">
      <HintPath>C:\Users\<USER>\AppData\Local\Temp\.net\CursorPro\q5SbC42L0TIT20ls1d54gL9BqQUflQ4=\Avalonia.Themes.Fluent.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>