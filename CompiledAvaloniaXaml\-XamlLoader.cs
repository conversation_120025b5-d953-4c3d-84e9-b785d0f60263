using System;
using System.ComponentModel;
using CursorPro;
using CursorPro.Views;

namespace CompiledAvaloniaXaml;

[EditorBrowsable(EditorBrowsableState.Never)]
public class _0021XamlLoader
{
	public static object TryLoad(IServiceProvider P_0, string P_1)
	{
		if (string.Equals(P_1, "avares://CursorPro/App.axaml", StringComparison.OrdinalIgnoreCase))
		{
			return new App();
		}
		if (string.Equals(P_1, "avares://CursorPro/Views/购买卡密.axaml", StringComparison.OrdinalIgnoreCase))
		{
			return new 购买卡密();
		}
		if (string.Equals(P_1, "avares://CursorPro/Views/主要窗口.axaml", StringComparison.OrdinalIgnoreCase))
		{
			return new 主要窗口();
		}
		return null;
	}

	public static object TryLoad(string P_0)
	{
		return TryLoad(null, P_0);
	}
}
